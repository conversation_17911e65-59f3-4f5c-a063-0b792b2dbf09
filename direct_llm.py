import openai

client = openai.OpenAI(api_key="your-api-key")

# 存储对话历史
messages = [
    {"role": "system", "content": "You are a helpful AI assistant."}
]

# 第一次对话
messages.append({"role": "user", "content": "What is AI?"})
response = client.chat.completions.create(model="gpt-3.5-turbo", messages=messages)
answer = response.choices[0].message.content
messages.append({"role": "assistant", "content": answer})
print("AI:", answer)

# 第二次对话，延续上下文
messages.append({"role": "user", "content": "Tell me more about its applications."})
response = client.chat.completions.create(model="gpt-3.5-turbo", messages=messages)
answer = response.choices[0].message.content
print("Applications:", answer)
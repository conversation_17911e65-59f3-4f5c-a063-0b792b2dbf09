from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain.memory import ConversationBufferMemory
from langchain_core.runnables import RunnablePassthrough

# 配置模型
llm = ChatOpenAI(api_key="your-api-key", model="gpt-3.5-turbo")

# 配置提示模板，包含历史消息占位符
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful AI assistant."),
    MessagesPlaceholder(variable_name="history"),
    ("user", "{input}")
])

# 配置记忆
memory = ConversationBufferMemory(return_messages=True)

# 创建链
chain = RunnablePassthrough.assign(
    history=lambda _: memory.load_memory_variables({})["history"]
) | prompt | llm | StrOutputParser()

# 第一次对话
result = chain.invoke({"input": "What is AI?"})
print("AI:", result)
memory.save_context({"input": "What is AI?"}, {"output": result})

# 第二次对话
result = chain.invoke({"input": "Tell Rambot more about its applications."})
print("Applications:", result)
memory.save_context({"input": "Tell me more about its applications."}, {"output": result})
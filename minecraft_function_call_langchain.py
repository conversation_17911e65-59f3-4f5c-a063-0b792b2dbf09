from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langchain.agents import create_tool_calling_agent, AgentExecutor

# 配置模型
llm = ChatOpenAI(
    api_key="sk-Ebzri9v1FOVCzqBFB74bX0ey4ZU79bnWlJn2J1OoxnyMoodj",  # 替换为你的 API Key
    model="gpt-3.5-turbo",
    base_url="https://api.chatanywhere.tech/v1"  # 可选，兼容接口的 endpoint
)

# 定义 Minecraft 工具
@tool
def give_item(player: str, item: str, quantity: int) -> str:
    """Give a specified item to a player in Minecraft."""
    command = f"/give {player} {item} {quantity}"
    print(f"Executing Minecraft command: {command}")
    return command

@tool
def teleport_player(player: str, location: str) -> str:
    """Teleport a player to a specified location in Minecraft."""
    command = f"/tp {player} {location}"
    print(f"Executing Minecraft command: {command}")
    return command

# 定义工具列表
tools = [give_item, teleport_player]

# 定义提示模板
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a Minecraft assistant that can execute server commands via tools."),
    ("user", "{input}"),
    ("placeholder", "{agent_scratchpad}")
])

# 创建工具调用代理
agent = create_tool_calling_agent(llm, tools, prompt)

# 创建代理执行器
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# 执行对话
input_text = "Give Steve 64 diamonds and teleport him to a village."
response = agent_executor.invoke({"input": input_text})

print("Final response:", response["output"])
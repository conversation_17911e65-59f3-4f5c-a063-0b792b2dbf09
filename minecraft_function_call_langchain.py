from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser

# 配置模型
llm = ChatOpenAI(
    api_key="sk-Ebzri9v1FOVCzqBFB74bX0ey4ZU79bnWlJn2J1OoxnyMoodj",  # 替换为你的 API Key
    model="gpt-3.5-turbo",
    base_url="https://api.chatanywhere.tech/v1"  # 可选，兼容接口的 endpoint
)

# 定义 Minecraft 工具
@tool
def give_item(player: str, item: str, quantity: int) -> str:
    """Give a specified item to a player in Minecraft."""
    command = f"/give {player} {item} {quantity}"
    print(f"Executing Minecraft command: {command}")
    return command

@tool
def teleport_player(player: str, location: str) -> str:
    """Teleport a player to a specified location in Minecraft."""
    command = f"/tp {player} {location}"
    print(f"Executing Minecraft command: {command}")
    return command

# 绑定工具到模型
llm_with_tools = llm.bind_tools([give_item, teleport_player])

# 定义提示模板
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a Minecraft assistant that can execute server commands via tools."),
    MessagesPlaceholder(variable_name="history", optional=True),
    ("user", "{input}")
])

# 创建链
chain = RunnablePassthrough.assign(
    input=lambda x: x["input"]
) | prompt | llm_with_tools | StrOutputParser()

# 执行对话
input_text = "Give Steve 64 diamonds and teleport him to a village."
response = chain.invoke({"input": input_text})

# 检查是否需要调用工具
if isinstance(response, str):
    print("Final response:", response)
else:
    # LangChain 自动处理工具调用，这里模拟输出
    print("Tool calls executed, results:")
    for tool_call in response:
        print(f"Tool result: {tool_call}")
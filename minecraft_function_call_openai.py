import openai
import json

# 配置 OpenAI 客户端
client = openai.OpenAI(
    api_key="sk-Ebzri9v1FOVCzqBFB74bX0ey4ZU79bnWlJn2J1OoxnyMoodj",  # 替换为你的 API Key
    base_url="https://api.chatanywhere.tech/v1"  # 或替换为兼容接口的 endpoint
)

# 定义 Minecraft 相关函数
tools = [
    {
        "type": "function",
        "function": {
            "name": "give_item",
            "description": "Give a specified item to a player in Minecraft.",
            "parameters": {
                "type": "object",
                "properties": {
                    "player": {"type": "string", "description": "The player's name"},
                    "item": {"type": "string", "description": "The item to give (e.g., diamond)"},
                    "quantity": {"type": "integer", "description": "Number of items to give"}
                },
                "required": ["player", "item", "quantity"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "teleport_player",
            "description": "Teleport a player to a specified location in Minecraft.",
            "parameters": {
                "type": "object",
                "properties": {
                    "player": {"type": "string", "description": "The player's name"},
                    "location": {"type": "string", "description": "The destination (e.g., village)"}
                },
                "required": ["player", "location"]
            }
        }
    }
]

# 模拟 Minecraft 服务器命令执行
def execute_give_item(player, item, quantity):
    command = f"/give {player} {item} {quantity}"
    print(f"Executing Minecraft command: {command}")
    return command

def execute_teleport_player(player, location):
    command = f"/tp {player} {location}"
    print(f"Executing Minecraft command: {command}")
    return command

# 处理模型返回的函数调用
def handle_function_call(tool_call):
    function_name = tool_call.function.name
    arguments = json.loads(tool_call.function.arguments)
    
    if function_name == "give_item":
        return execute_give_item(
            arguments["player"],
            arguments["item"],
            arguments["quantity"]
        )
    elif function_name == "teleport_player":
        return execute_teleport_player(
            arguments["player"],
            arguments["location"]
        )
    return None

# 主对话逻辑
messages = [
    {"role": "system", "content": "You are a Minecraft assistant that can execute server commands via function calls."},
    {"role": "user", "content": "Give Steve 64 diamonds and teleport him to a village."}
]

# 发起请求
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=messages,
    tools=tools,
    tool_choice="auto"
)

# 处理响应
response_message = response.choices[0].message
messages.append(response_message)

# 检查是否需要调用函数
if response_message.tool_calls:
    for tool_call in response_message.tool_calls:
        result = handle_function_call(tool_call)
        messages.append({
            "role": "tool",
            "content": result,
            "tool_call_id": tool_call.id
        })

    # 继续对话以获取最终响应
    final_response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=messages
    )
    print("Final response:", final_response.choices[0].message.content)
else:
    print("Final response:", response_message.content)